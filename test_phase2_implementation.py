#!/usr/bin/env python3
"""
Test script for Phase 2 implementation of embedding and retrieval system optimization.

This script tests the four main components implemented in Phase 2:
1. Enhanced Relevance Scoring System
2. Asynchronous Processing Pipeline
3. Query Optimization and Expansion
4. Performance Monitoring and Analytics
"""

import os
import sys
import logging
import asyncio
import time
from typing import List, Dict

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_enhanced_relevance_scorer():
    """Test the enhanced relevance scoring system."""
    logger.info("Testing Enhanced Relevance Scoring System...")
    
    try:
        from app.services.enhanced_relevance_scorer import (
            get_enhanced_relevance_scorer,
            score_document_relevance_enhanced,
            filter_relevant_documents_enhanced
        )
        from langchain.schema import Document
        
        # Test scorer initialization
        scorer = get_enhanced_relevance_scorer()
        logger.info(f"✓ Enhanced relevance scorer initialized successfully")
        
        # Test configuration and stats
        stats = scorer.get_stats()
        logger.info(f"✓ Scorer stats: {stats}")
        
        # Test document scoring
        test_doc = Document(
            page_content="This document discusses biodiversity conservation in marine ecosystems. "
                        "The research focuses on coral reef protection and sustainable fishing practices.",
            metadata={"source": "test_document.pdf", "category": "marine"}
        )
        
        test_query = "marine biodiversity conservation"
        
        # Test individual document scoring
        score = score_document_relevance_enhanced(test_doc, test_query)
        logger.info(f"✓ Document relevance score: {score:.3f}")
        
        if 0.0 <= score <= 1.0:
            logger.info(f"✓ Score within valid range")
        else:
            logger.error(f"❌ Score outside valid range: {score}")
            return False
        
        # Test batch document filtering
        test_docs = [
            Document(page_content="Marine biodiversity research and conservation efforts", metadata={"source": "doc1.pdf"}),
            Document(page_content="Forest management and sustainable logging practices", metadata={"source": "doc2.pdf"}),
            Document(page_content="Coral reef ecosystem protection and marine conservation", metadata={"source": "doc3.pdf"}),
            Document(page_content="Urban planning and city development", metadata={"source": "doc4.pdf"})
        ]
        
        filtered_docs = filter_relevant_documents_enhanced(test_docs, test_query, threshold=0.1)
        logger.info(f"✓ Filtered {len(filtered_docs)} relevant documents from {len(test_docs)} total")
        
        # Verify sorting by relevance
        if len(filtered_docs) > 1:
            scores = [score for _, score in filtered_docs]
            if scores == sorted(scores, reverse=True):
                logger.info(f"✓ Documents properly sorted by relevance")
            else:
                logger.error(f"❌ Documents not properly sorted")
                return False
        
        # Test corpus fitting
        corpus = [doc.page_content for doc in test_docs]
        scorer.fit_corpus(corpus)
        logger.info(f"✓ TF-IDF vectorizer fitted on corpus")
        
        # Test scoring after fitting
        score_after_fit = score_document_relevance_enhanced(test_doc, test_query)
        logger.info(f"✓ Score after TF-IDF fitting: {score_after_fit:.3f}")
        
        logger.info("✅ Enhanced Relevance Scoring System tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Enhanced Relevance Scoring System test failed: {e}")
        return False

def test_query_optimizer():
    """Test the query optimization and expansion system."""
    logger.info("Testing Query Optimization and Expansion System...")
    
    try:
        from app.services.query_optimizer import (
            get_query_optimizer,
            optimize_query,
            analyze_query
        )
        
        # Test optimizer initialization
        optimizer = get_query_optimizer()
        logger.info(f"✓ Query optimizer initialized successfully")
        
        # Test optimization stats
        stats = optimizer.get_optimization_stats()
        logger.info(f"✓ Optimizer stats: {stats}")
        
        # Test query optimization with different strategies
        test_queries = [
            "marine biodiversity conservation",
            "ERDB forest research",
            "climate change impact on ecosystems",
            "sustainable development goals"
        ]
        
        strategies = ['conservative', 'balanced', 'aggressive']
        
        for query in test_queries:
            logger.info(f"Testing query: '{query}'")
            
            for strategy in strategies:
                optimized = optimize_query(query, strategy)
                logger.info(f"  {strategy}: '{optimized}'")
                
                # Verify optimization doesn't return empty string
                if not optimized.strip():
                    logger.error(f"❌ Optimization returned empty string for strategy {strategy}")
                    return False
            
            # Test query analysis
            analysis = analyze_query(query)
            logger.info(f"  Analysis: {analysis['word_count']} words, "
                       f"{analysis['domain_terms_count']} domain terms, "
                       f"strategy: {analysis['suggested_strategy']}")
        
        # Test abbreviation expansion
        abbrev_query = "ERDB and DENR collaboration on CBD implementation"
        expanded = optimize_query(abbrev_query, 'balanced')
        logger.info(f"✓ Abbreviation expansion test: '{abbrev_query}' -> '{expanded}'")
        
        # Test synonym expansion
        synonym_query = "forest conservation research"
        expanded_syn = optimize_query(synonym_query, 'aggressive')
        logger.info(f"✓ Synonym expansion test: '{synonym_query}' -> '{expanded_syn}'")
        
        logger.info("✅ Query Optimization and Expansion System tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Query Optimization and Expansion System test failed: {e}")
        return False

async def test_async_processing():
    """Test the asynchronous processing pipeline."""
    logger.info("Testing Asynchronous Processing Pipeline...")
    
    try:
        from app.services.async_processing import (
            get_async_document_processor,
            process_document_async
        )
        
        # Test processor initialization
        processor = get_async_document_processor()
        logger.info(f"✓ Async document processor initialized successfully")
        
        # Test processing stats
        stats = processor.get_processing_stats()
        logger.info(f"✓ Initial processing stats: {stats}")
        
        # Create a mock document for testing
        import tempfile
        import os
        
        # Create a simple test PDF content (mock)
        test_content = b"%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n"
        
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
            temp_file.write(test_content)
            temp_path = temp_file.name
        
        try:
            # Test async document processing (will likely fail due to invalid PDF, but tests the pipeline)
            result = await process_document_async(
                temp_path, 
                'test_category',
                source_url='http://test.com/doc.pdf'
            )
            
            logger.info(f"✓ Async processing completed with status: {result.get('status')}")
            
            # Test batch processing
            documents = [
                {'path': temp_path, 'options': {'source_url': 'http://test1.com'}},
                {'path': temp_path, 'options': {'source_url': 'http://test2.com'}}
            ]
            
            batch_results = await processor.batch_process_documents(documents, 'test_category')
            logger.info(f"✓ Batch processing completed: {len(batch_results)} results")
            
            # Test stats after processing
            final_stats = processor.get_processing_stats()
            logger.info(f"✓ Final processing stats: {final_stats}")
            
        finally:
            # Clean up
            try:
                os.unlink(temp_path)
            except:
                pass
        
        logger.info("✅ Asynchronous Processing Pipeline tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Asynchronous Processing Pipeline test failed: {e}")
        return False

def test_performance_monitor():
    """Test the performance monitoring and analytics system."""
    logger.info("Testing Performance Monitoring and Analytics System...")
    
    try:
        from app.services.performance_monitor import (
            get_performance_monitor,
            monitor_performance,
            record_embedding_time,
            record_query_time,
            record_retrieval_time
        )
        
        # Test monitor initialization
        monitor = get_performance_monitor()
        logger.info(f"✓ Performance monitor initialized successfully")
        
        # Test metric recording
        record_embedding_time(0.5, True, 'test-model')
        record_query_time(0.2, True, 50)
        record_retrieval_time(0.3, True, 10)
        
        logger.info(f"✓ Metrics recorded successfully")
        
        # Test cache statistics
        monitor.record_cache_hit('embedding_cache')
        monitor.record_cache_hit('embedding_cache')
        monitor.record_cache_miss('embedding_cache')
        
        cache_stats = monitor.get_cache_stats()
        logger.info(f"✓ Cache stats: {cache_stats}")
        
        # Test operation statistics
        operation_stats = monitor.get_operation_stats()
        logger.info(f"✓ Operation stats: {list(operation_stats.keys())}")
        
        # Test performance summary
        summary = monitor.get_performance_summary()
        logger.info(f"✓ Performance summary generated with {summary['summary']['total_operations']} operations")
        
        # Test recent metrics
        recent_metrics = monitor.get_recent_metrics(minutes=5)
        logger.info(f"✓ Retrieved {len(recent_metrics)} recent metrics")
        
        # Test decorator functionality
        @monitor_performance('test_operation')
        def test_function():
            time.sleep(0.1)
            return "test_result"
        
        result = test_function()
        logger.info(f"✓ Decorator test completed: {result}")
        
        # Test metrics export
        export_path = monitor.export_metrics()
        logger.info(f"✓ Metrics exported to: {export_path}")
        
        # Verify export file exists
        if os.path.exists(export_path):
            logger.info(f"✓ Export file verified")
        else:
            logger.error(f"❌ Export file not found")
            return False
        
        logger.info("✅ Performance Monitoring and Analytics System tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Performance Monitoring and Analytics System test failed: {e}")
        return False

def test_integration():
    """Test integration between Phase 2 components."""
    logger.info("Testing Phase 2 Component Integration...")
    
    try:
        from app.services.enhanced_relevance_scorer import get_enhanced_relevance_scorer
        from app.services.query_optimizer import optimize_query
        from app.services.performance_monitor import get_performance_monitor
        from langchain.schema import Document
        
        # Test integrated workflow
        monitor = get_performance_monitor()
        scorer = get_enhanced_relevance_scorer()
        
        # Simulate a complete query workflow
        original_query = "marine biodiversity research"
        
        # Step 1: Optimize query
        start_time = time.time()
        optimized_query = optimize_query(original_query, 'balanced')
        optimization_time = time.time() - start_time
        monitor.record_metric('query_optimization', optimization_time, True)
        
        # Step 2: Score documents with enhanced scorer
        test_docs = [
            Document(page_content="Marine biodiversity conservation research", metadata={"source": "doc1.pdf"}),
            Document(page_content="Terrestrial ecosystem management", metadata={"source": "doc2.pdf"})
        ]
        
        start_time = time.time()
        scored_docs = scorer.batch_score_documents(test_docs, optimized_query)
        scoring_time = time.time() - start_time
        monitor.record_metric('enhanced_scoring', scoring_time, True)
        
        # Step 3: Check performance metrics
        stats = monitor.get_operation_stats()
        
        if 'query_optimization' in stats and 'enhanced_scoring' in stats:
            logger.info(f"✓ Integration workflow completed successfully")
            logger.info(f"  Query optimization: {stats['query_optimization']['avg_time']:.3f}s avg")
            logger.info(f"  Enhanced scoring: {stats['enhanced_scoring']['avg_time']:.3f}s avg")
        else:
            logger.error(f"❌ Integration metrics not recorded properly")
            return False
        
        logger.info("✅ Phase 2 Component Integration tests passed!")
        return True
        
    except Exception as e:
        logger.error(f"❌ Phase 2 Component Integration test failed: {e}")
        return False

async def main():
    """Run all Phase 2 tests."""
    logger.info("🚀 Starting Phase 2 Implementation Tests...")
    
    tests = [
        ("Enhanced Relevance Scoring", test_enhanced_relevance_scorer),
        ("Query Optimization", test_query_optimizer),
        ("Asynchronous Processing", test_async_processing),
        ("Performance Monitoring", test_performance_monitor),
        ("Component Integration", test_integration)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running {test_name} Test")
        logger.info(f"{'='*50}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            logger.error(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        logger.info(f"{test_name}: {status}")
        if result:
            passed += 1
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All Phase 2 implementation tests passed!")
        return 0
    else:
        logger.error(f"💥 {total - passed} tests failed!")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
